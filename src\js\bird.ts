export class Bird {
  public x: number
  public y: number
  public width: number = 40
  public height: number = 30
  
  private velocity: number = 0
  private rotation: number = 0
  
  // Physics constants
  private readonly GRAVITY = 0.0008
  private readonly JUMP_FORCE = -0.6
  private readonly MAX_FALL_SPEED = 0.8
  private readonly MAX_ROTATION = Math.PI / 3 // 60 degrees
  private readonly ROTATION_SPEED = 0.003

  constructor(x: number, y: number) {
    this.x = x
    this.y = y
  }

  public update(deltaTime: number): void {
    // Apply gravity
    this.velocity += this.GRAVITY * deltaTime
    
    // Limit fall speed
    if (this.velocity > this.MAX_FALL_SPEED) {
      this.velocity = this.MAX_FALL_SPEED
    }
    
    // Update position
    this.y += this.velocity * deltaTime
    
    // Update rotation based on velocity
    if (this.velocity < 0) {
      // Going up - rotate upward
      this.rotation = Math.max(-this.MAX_ROTATION, this.velocity * 2)
    } else {
      // Falling - rotate downward
      this.rotation = Math.min(this.MAX_ROTATION, this.velocity * 2)
    }
  }

  public jump(): void {
    this.velocity = this.JUMP_FORCE
  }

  public reset(x: number, y: number): void {
    this.x = x
    this.y = y
    this.velocity = 0
    this.rotation = 0
  }

  public render(ctx: CanvasRenderingContext2D): void {
    ctx.save()
    
    // Move to bird position
    ctx.translate(this.x + this.width / 2, this.y + this.height / 2)
    ctx.rotate(this.rotation)
    
    // Draw bird body (simple oval)
    ctx.fillStyle = '#FFD700' // Golden yellow
    ctx.beginPath()
    ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2)
    ctx.fill()
    
    // Draw bird outline
    ctx.strokeStyle = '#FFA500' // Orange outline
    ctx.lineWidth = 2
    ctx.stroke()
    
    // Draw wing
    ctx.fillStyle = '#FF8C00' // Dark orange wing
    ctx.beginPath()
    ctx.ellipse(-5, -2, 12, 8, 0, 0, Math.PI * 2)
    ctx.fill()
    
    // Draw eye
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(8, -5, 6, 6, 0, 0, Math.PI * 2)
    ctx.fill()
    
    // Draw pupil
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(10, -5, 3, 3, 0, 0, Math.PI * 2)
    ctx.fill()
    
    // Draw beak
    ctx.fillStyle = '#FF4500' // Red-orange beak
    ctx.beginPath()
    ctx.moveTo(this.width / 2 - 5, 0)
    ctx.lineTo(this.width / 2 + 5, 2)
    ctx.lineTo(this.width / 2 - 2, 5)
    ctx.closePath()
    ctx.fill()
    
    ctx.restore()
  }

  // Get bounding box for collision detection
  public getBounds(): { x: number, y: number, width: number, height: number } {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    }
  }
}
