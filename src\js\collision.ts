import { Bird } from './bird'
import { Pipe } from './pipes'

export class CollisionDetector {
  
  public checkCollisions(bird: Bird, pipes: Pipe[], groundY: number): boolean {
    // Check ground collision
    if (this.checkGroundCollision(bird, groundY)) {
      return true
    }
    
    // Check ceiling collision
    if (this.checkCeilingCollision(bird)) {
      return true
    }
    
    // Check pipe collisions
    for (const pipe of pipes) {
      if (this.checkPipeCollision(bird, pipe)) {
        return true
      }
    }
    
    return false
  }

  private checkGroundCollision(bird: Bird, groundY: number): boolean {
    const birdBounds = bird.getBounds()
    return birdBounds.y + birdBounds.height >= groundY
  }

  private checkCeilingCollision(bird: Bird): boolean {
    const birdBounds = bird.getBounds()
    return birdBounds.y <= 0
  }

  private checkPipeCollision(bird: Bird, pipe: Pipe): boolean {
    const birdBounds = bird.getBounds()
    
    // Check if bird is horizontally aligned with pipe
    if (birdBounds.x + birdBounds.width > pipe.x && 
        birdBounds.x < pipe.x + pipe.width) {
      
      // Check collision with top pipe
      if (birdBounds.y < pipe.topHeight) {
        return true
      }
      
      // Check collision with bottom pipe
      if (birdBounds.y + birdBounds.height > pipe.bottomY) {
        return true
      }
    }
    
    return false
  }

  // More precise collision detection using circle-rectangle collision
  public checkPreciseCollision(bird: Bird, pipe: Pipe): boolean {
    const birdCenterX = bird.x + bird.width / 2
    const birdCenterY = bird.y + bird.height / 2
    const birdRadius = Math.min(bird.width, bird.height) / 2
    
    // Check collision with top pipe
    if (this.circleRectCollision(
      birdCenterX, birdCenterY, birdRadius,
      pipe.x, 0, pipe.width, pipe.topHeight
    )) {
      return true
    }
    
    // Check collision with bottom pipe
    const bottomPipeHeight = 600 - 100 - pipe.bottomY // Assuming canvas height 600 and ground height 100
    if (this.circleRectCollision(
      birdCenterX, birdCenterY, birdRadius,
      pipe.x, pipe.bottomY, pipe.width, bottomPipeHeight
    )) {
      return true
    }
    
    return false
  }

  private circleRectCollision(
    circleX: number, circleY: number, radius: number,
    rectX: number, rectY: number, rectWidth: number, rectHeight: number
  ): boolean {
    // Find the closest point on the rectangle to the circle
    const closestX = Math.max(rectX, Math.min(circleX, rectX + rectWidth))
    const closestY = Math.max(rectY, Math.min(circleY, rectY + rectHeight))
    
    // Calculate distance between circle center and closest point
    const distanceX = circleX - closestX
    const distanceY = circleY - closestY
    const distanceSquared = distanceX * distanceX + distanceY * distanceY
    
    // Check if distance is less than radius
    return distanceSquared < radius * radius
  }
}
