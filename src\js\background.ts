interface Cloud {
  x: number
  y: number
  size: number
  speed: number
  opacity: number
}

export class BackgroundManager {
  private clouds: Cloud[] = []
  private groundOffset: number = 0
  private canvasWidth: number
  private canvasHeight: number
  private groundHeight: number

  constructor(canvasWidth: number, canvasHeight: number, groundHeight: number) {
    this.canvasWidth = canvasWidth
    this.canvasHeight = canvasHeight
    this.groundHeight = groundHeight
    this.initializeClouds()
  }

  private initializeClouds(): void {
    // Create initial clouds
    for (let i = 0; i < 8; i++) {
      this.clouds.push({
        x: Math.random() * this.canvasWidth * 2, // Spread across wider area
        y: Math.random() * (this.canvasHeight - this.groundHeight - 100) + 50,
        size: 20 + Math.random() * 40,
        speed: 0.02 + Math.random() * 0.03, // Different speeds for parallax
        opacity: 0.3 + Math.random() * 0.4
      })
    }
  }

  public update(deltaTime: number): void {
    // Update ground scrolling
    this.groundOffset += 0.05 * deltaTime
    if (this.groundOffset >= 20) {
      this.groundOffset = 0
    }

    // Update clouds
    for (const cloud of this.clouds) {
      cloud.x -= cloud.speed * deltaTime
      
      // Reset cloud position when it goes off screen
      if (cloud.x + cloud.size < 0) {
        cloud.x = this.canvasWidth + Math.random() * 200
        cloud.y = Math.random() * (this.canvasHeight - this.groundHeight - 100) + 50
        cloud.size = 20 + Math.random() * 40
        cloud.opacity = 0.3 + Math.random() * 0.4
      }
    }
  }

  public renderSky(ctx: CanvasRenderingContext2D): void {
    // Enhanced sky gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, this.canvasHeight - this.groundHeight)
    gradient.addColorStop(0, '#87CEEB') // Sky blue
    gradient.addColorStop(0.3, '#98D8E8') // Light blue
    gradient.addColorStop(0.7, '#B0E0E6') // Powder blue
    gradient.addColorStop(1, '#E0F6FF') // Very light blue
    
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight - this.groundHeight)

    // Add sun
    this.renderSun(ctx)
  }

  private renderSun(ctx: CanvasRenderingContext2D): void {
    const sunX = this.canvasWidth - 120
    const sunY = 80
    const sunRadius = 40

    // Sun glow
    const sunGradient = ctx.createRadialGradient(sunX, sunY, 0, sunX, sunY, sunRadius * 1.5)
    sunGradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)')
    sunGradient.addColorStop(0.5, 'rgba(255, 255, 0, 0.4)')
    sunGradient.addColorStop(1, 'rgba(255, 255, 0, 0)')
    
    ctx.fillStyle = sunGradient
    ctx.beginPath()
    ctx.arc(sunX, sunY, sunRadius * 1.5, 0, Math.PI * 2)
    ctx.fill()

    // Sun body
    ctx.fillStyle = '#FFD700'
    ctx.beginPath()
    ctx.arc(sunX, sunY, sunRadius, 0, Math.PI * 2)
    ctx.fill()

    // Sun rays
    ctx.strokeStyle = '#FFD700'
    ctx.lineWidth = 3
    for (let i = 0; i < 8; i++) {
      const angle = (Math.PI * 2 * i) / 8
      const startX = sunX + Math.cos(angle) * (sunRadius + 10)
      const startY = sunY + Math.sin(angle) * (sunRadius + 10)
      const endX = sunX + Math.cos(angle) * (sunRadius + 25)
      const endY = sunY + Math.sin(angle) * (sunRadius + 25)
      
      ctx.beginPath()
      ctx.moveTo(startX, startY)
      ctx.lineTo(endX, endY)
      ctx.stroke()
    }
  }

  public renderClouds(ctx: CanvasRenderingContext2D): void {
    for (const cloud of this.clouds) {
      this.renderCloud(ctx, cloud)
    }
  }

  private renderCloud(ctx: CanvasRenderingContext2D, cloud: Cloud): void {
    ctx.save()
    ctx.globalAlpha = cloud.opacity
    ctx.fillStyle = '#FFFFFF'

    // Draw cloud as multiple overlapping circles
    const numCircles = 5
    for (let i = 0; i < numCircles; i++) {
      const offsetX = (i - 2) * cloud.size * 0.3
      const offsetY = Math.sin(i) * cloud.size * 0.2
      const circleSize = cloud.size * (0.6 + Math.random() * 0.4)
      
      ctx.beginPath()
      ctx.arc(cloud.x + offsetX, cloud.y + offsetY, circleSize, 0, Math.PI * 2)
      ctx.fill()
    }

    ctx.restore()
  }

  public renderGround(ctx: CanvasRenderingContext2D): void {
    const groundY = this.canvasHeight - this.groundHeight

    // Ground base
    ctx.fillStyle = '#DEB887'
    ctx.fillRect(0, groundY, this.canvasWidth, this.groundHeight)

    // Animated ground texture
    ctx.strokeStyle = '#CD853F'
    ctx.lineWidth = 2
    for (let i = -20; i < this.canvasWidth + 20; i += 20) {
      const x = i - this.groundOffset
      ctx.beginPath()
      ctx.moveTo(x, groundY)
      ctx.lineTo(x, this.canvasHeight)
      ctx.stroke()
    }

    // Ground grass details
    ctx.strokeStyle = '#8FBC8F'
    ctx.lineWidth = 1
    for (let i = 0; i < this.canvasWidth; i += 15) {
      const x = i - (this.groundOffset * 0.5) % 15
      const grassHeight = 5 + Math.random() * 8
      
      ctx.beginPath()
      ctx.moveTo(x, groundY)
      ctx.lineTo(x + Math.random() * 2 - 1, groundY - grassHeight)
      ctx.stroke()
    }

    // Ground shadow/depth
    const shadowGradient = ctx.createLinearGradient(0, groundY, 0, groundY + 30)
    shadowGradient.addColorStop(0, 'rgba(0, 0, 0, 0.1)')
    shadowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)')
    
    ctx.fillStyle = shadowGradient
    ctx.fillRect(0, groundY, this.canvasWidth, 30)
  }

  public reset(): void {
    this.groundOffset = 0
    // Randomize cloud positions
    for (const cloud of this.clouds) {
      cloud.x = Math.random() * this.canvasWidth * 2
      cloud.y = Math.random() * (this.canvasHeight - this.groundHeight - 100) + 50
    }
  }
}
