export interface Pipe {
  x: number
  topHeight: number
  bottomY: number
  width: number
  scored: boolean
}

export class PipeManager {
  private pipes: Pipe[] = []
  private canvasWidth: number
  private canvasHeight: number
  private groundHeight: number
  
  // Pipe constants
  private readonly PIPE_WIDTH = 80
  private readonly BASE_PIPE_GAP = 200
  private readonly BASE_PIPE_SPEED = 0.2
  private readonly PIPE_SPACING = 300
  private readonly MIN_PIPE_HEIGHT = 50

  // Difficulty modifiers
  private speedMultiplier: number = 1
  private gapReduction: number = 0
  
  constructor(canvasWidth: number, canvasHeight: number, groundHeight: number) {
    this.canvasWidth = canvasWidth
    this.canvasHeight = canvasHeight
    this.groundHeight = groundHeight
  }

  public update(deltaTime: number): void {
    // Move existing pipes
    const currentSpeed = this.BASE_PIPE_SPEED * this.speedMultiplier
    for (let i = this.pipes.length - 1; i >= 0; i--) {
      const pipe = this.pipes[i]
      pipe.x -= currentSpeed * deltaTime
      
      // Remove pipes that are off screen
      if (pipe.x + this.PIPE_WIDTH < 0) {
        this.pipes.splice(i, 1)
      }
    }
    
    // Add new pipes
    this.addNewPipeIfNeeded()
  }

  private addNewPipeIfNeeded(): void {
    // Add first pipe or check if we need a new one
    if (this.pipes.length === 0 || 
        this.pipes[this.pipes.length - 1].x < this.canvasWidth - this.PIPE_SPACING) {
      this.addPipe()
    }
  }

  private addPipe(): void {
    const currentGap = this.BASE_PIPE_GAP - this.gapReduction
    const availableHeight = this.canvasHeight - this.groundHeight - currentGap
    const maxTopHeight = availableHeight - this.MIN_PIPE_HEIGHT
    const minTopHeight = this.MIN_PIPE_HEIGHT

    const topHeight = Math.random() * (maxTopHeight - minTopHeight) + minTopHeight
    const bottomY = topHeight + currentGap
    
    const pipe: Pipe = {
      x: this.canvasWidth,
      topHeight: topHeight,
      bottomY: bottomY,
      width: this.PIPE_WIDTH,
      scored: false
    }
    
    this.pipes.push(pipe)
  }

  public render(ctx: CanvasRenderingContext2D): void {
    for (const pipe of this.pipes) {
      this.renderPipe(ctx, pipe)
    }
  }

  private renderPipe(ctx: CanvasRenderingContext2D, pipe: Pipe): void {
    const gradient = ctx.createLinearGradient(pipe.x, 0, pipe.x + pipe.width, 0)
    gradient.addColorStop(0, '#228B22') // Forest green
    gradient.addColorStop(0.5, '#32CD32') // Lime green
    gradient.addColorStop(1, '#228B22') // Forest green
    
    ctx.fillStyle = gradient
    ctx.strokeStyle = '#006400' // Dark green outline
    ctx.lineWidth = 3
    
    // Draw top pipe
    ctx.fillRect(pipe.x, 0, pipe.width, pipe.topHeight)
    ctx.strokeRect(pipe.x, 0, pipe.width, pipe.topHeight)
    
    // Draw top pipe cap
    const capHeight = 30
    ctx.fillRect(pipe.x - 5, pipe.topHeight - capHeight, pipe.width + 10, capHeight)
    ctx.strokeRect(pipe.x - 5, pipe.topHeight - capHeight, pipe.width + 10, capHeight)
    
    // Draw bottom pipe
    const bottomHeight = this.canvasHeight - this.groundHeight - pipe.bottomY
    ctx.fillRect(pipe.x, pipe.bottomY, pipe.width, bottomHeight)
    ctx.strokeRect(pipe.x, pipe.bottomY, pipe.width, bottomHeight)
    
    // Draw bottom pipe cap
    ctx.fillRect(pipe.x - 5, pipe.bottomY, pipe.width + 10, capHeight)
    ctx.strokeRect(pipe.x - 5, pipe.bottomY, pipe.width + 10, capHeight)
    
    // Add pipe texture
    this.addPipeTexture(ctx, pipe)
  }

  private addPipeTexture(ctx: CanvasRenderingContext2D, pipe: Pipe): void {
    ctx.strokeStyle = '#006400'
    ctx.lineWidth = 1
    
    // Vertical lines for texture
    for (let i = 0; i < 3; i++) {
      const x = pipe.x + (pipe.width / 4) * (i + 1)
      
      // Top pipe texture
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, pipe.topHeight)
      ctx.stroke()
      
      // Bottom pipe texture
      ctx.beginPath()
      ctx.moveTo(x, pipe.bottomY)
      ctx.lineTo(x, this.canvasHeight - this.groundHeight)
      ctx.stroke()
    }
  }

  public getPipes(): Pipe[] {
    return this.pipes
  }

  public checkForScore(birdX: number): number {
    let newScores = 0
    
    for (const pipe of this.pipes) {
      if (!pipe.scored && birdX > pipe.x + pipe.width) {
        pipe.scored = true
        newScores++
      }
    }
    
    return newScores
  }

  public reset(): void {
    this.pipes = []
    this.speedMultiplier = 1
    this.gapReduction = 0
  }

  public setSpeedMultiplier(multiplier: number): void {
    this.speedMultiplier = multiplier
  }

  public setGapReduction(reduction: number): void {
    this.gapReduction = reduction
  }
}
