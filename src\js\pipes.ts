export interface Pipe {
  x: number
  topHeight: number
  bottomY: number
  width: number
  scored: boolean
}

export class PipeManager {
  private pipes: Pipe[] = []
  private pipePool: Pipe[] = []
  private canvasWidth: number
  private canvasHeight: number
  private groundHeight: number

  // Pipe constants
  private readonly PIPE_WIDTH = 80
  private readonly BASE_PIPE_GAP = 200
  private readonly BASE_PIPE_SPEED = 0.2
  private readonly PIPE_SPACING = 300
  private readonly MIN_PIPE_HEIGHT = 50
  private readonly MAX_POOL_SIZE = 10

  // Difficulty modifiers
  private speedMultiplier: number = 1
  private gapReduction: number = 0

  // Cached gradients for performance
  private pipeGradient: CanvasGradient | null = null
  
  constructor(canvasWidth: number, canvasHeight: number, groundHeight: number) {
    this.canvasWidth = canvasWidth
    this.canvasHeight = canvasHeight
    this.groundHeight = groundHeight
    this.initializePool()
  }

  private initializePool(): void {
    // Pre-create pipe objects for object pooling
    for (let i = 0; i < this.MAX_POOL_SIZE; i++) {
      this.pipePool.push({
        x: 0,
        topHeight: 0,
        bottomY: 0,
        width: this.PIPE_WIDTH,
        scored: false
      })
    }
  }

  private getPipeFromPool(): Pipe | null {
    return this.pipePool.pop() || null
  }

  private returnPipeToPool(pipe: Pipe): void {
    if (this.pipePool.length < this.MAX_POOL_SIZE) {
      pipe.scored = false
      this.pipePool.push(pipe)
    }
  }

  public update(deltaTime: number): void {
    // Move existing pipes
    const currentSpeed = this.BASE_PIPE_SPEED * this.speedMultiplier
    for (let i = this.pipes.length - 1; i >= 0; i--) {
      const pipe = this.pipes[i]
      pipe.x -= currentSpeed * deltaTime

      // Remove pipes that are off screen and return to pool
      if (pipe.x + this.PIPE_WIDTH < 0) {
        const removedPipe = this.pipes.splice(i, 1)[0]
        this.returnPipeToPool(removedPipe)
      }
    }

    // Add new pipes
    this.addNewPipeIfNeeded()
  }

  private addNewPipeIfNeeded(): void {
    // Add first pipe or check if we need a new one
    if (this.pipes.length === 0 || 
        this.pipes[this.pipes.length - 1].x < this.canvasWidth - this.PIPE_SPACING) {
      this.addPipe()
    }
  }

  private addPipe(): void {
    const currentGap = this.BASE_PIPE_GAP - this.gapReduction
    const availableHeight = this.canvasHeight - this.groundHeight - currentGap
    const maxTopHeight = availableHeight - this.MIN_PIPE_HEIGHT
    const minTopHeight = this.MIN_PIPE_HEIGHT

    const topHeight = Math.random() * (maxTopHeight - minTopHeight) + minTopHeight
    const bottomY = topHeight + currentGap

    // Try to get pipe from pool, create new one if pool is empty
    let pipe = this.getPipeFromPool()
    if (!pipe) {
      pipe = {
        x: this.canvasWidth,
        topHeight: topHeight,
        bottomY: bottomY,
        width: this.PIPE_WIDTH,
        scored: false
      }
    } else {
      // Reuse existing pipe object
      pipe.x = this.canvasWidth
      pipe.topHeight = topHeight
      pipe.bottomY = bottomY
      pipe.scored = false
    }

    this.pipes.push(pipe)
  }

  public render(ctx: CanvasRenderingContext2D): void {
    // Create gradient once if not cached
    if (!this.pipeGradient) {
      this.pipeGradient = ctx.createLinearGradient(0, 0, this.PIPE_WIDTH, 0)
      this.pipeGradient.addColorStop(0, '#228B22') // Forest green
      this.pipeGradient.addColorStop(0.5, '#32CD32') // Lime green
      this.pipeGradient.addColorStop(1, '#228B22') // Forest green
    }

    for (const pipe of this.pipes) {
      this.renderPipe(ctx, pipe)
    }
  }

  private renderPipe(ctx: CanvasRenderingContext2D, pipe: Pipe): void {
    // Use cached gradient and translate it to pipe position
    ctx.save()
    ctx.translate(pipe.x, 0)

    ctx.fillStyle = this.pipeGradient!
    ctx.strokeStyle = '#006400' // Dark green outline
    ctx.lineWidth = 3
    
    // Draw top pipe (using local coordinates due to translate)
    ctx.fillRect(0, 0, pipe.width, pipe.topHeight)
    ctx.strokeRect(0, 0, pipe.width, pipe.topHeight)

    // Draw top pipe cap
    const capHeight = 30
    ctx.fillRect(-5, pipe.topHeight - capHeight, pipe.width + 10, capHeight)
    ctx.strokeRect(-5, pipe.topHeight - capHeight, pipe.width + 10, capHeight)

    // Draw bottom pipe
    const bottomHeight = this.canvasHeight - this.groundHeight - pipe.bottomY
    ctx.fillRect(0, pipe.bottomY, pipe.width, bottomHeight)
    ctx.strokeRect(0, pipe.bottomY, pipe.width, bottomHeight)

    // Draw bottom pipe cap
    ctx.fillRect(-5, pipe.bottomY, pipe.width + 10, capHeight)
    ctx.strokeRect(-5, pipe.bottomY, pipe.width + 10, capHeight)
    
    // Add pipe texture
    this.addPipeTexture(ctx, pipe)

    ctx.restore()
  }

  private addPipeTexture(ctx: CanvasRenderingContext2D, pipe: Pipe): void {
    ctx.strokeStyle = '#006400'
    ctx.lineWidth = 1

    // Vertical lines for texture
    for (let i = 0; i < 3; i++) {
      const x = pipe.x + (pipe.width / 4) * (i + 1)

      // Top pipe texture
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, pipe.topHeight)
      ctx.stroke()

      // Bottom pipe texture
      ctx.beginPath()
      ctx.moveTo(x, pipe.bottomY)
      ctx.lineTo(x, this.canvasHeight - this.groundHeight)
      ctx.stroke()
    }

    // Add horizontal texture lines
    ctx.strokeStyle = '#228B22'
    ctx.lineWidth = 0.5

    // Top pipe horizontal lines
    for (let y = 10; y < pipe.topHeight; y += 15) {
      ctx.beginPath()
      ctx.moveTo(pipe.x, y)
      ctx.lineTo(pipe.x + pipe.width, y)
      ctx.stroke()
    }

    // Bottom pipe horizontal lines
    for (let y = pipe.bottomY + 10; y < this.canvasHeight - this.groundHeight; y += 15) {
      ctx.beginPath()
      ctx.moveTo(pipe.x, y)
      ctx.lineTo(pipe.x + pipe.width, y)
      ctx.stroke()
    }

    // Add pipe highlights
    ctx.strokeStyle = '#90EE90'
    ctx.lineWidth = 1

    // Left highlight
    ctx.beginPath()
    ctx.moveTo(pipe.x + 2, 0)
    ctx.lineTo(pipe.x + 2, pipe.topHeight)
    ctx.stroke()

    ctx.beginPath()
    ctx.moveTo(pipe.x + 2, pipe.bottomY)
    ctx.lineTo(pipe.x + 2, this.canvasHeight - this.groundHeight)
    ctx.stroke()
  }

  public getPipes(): Pipe[] {
    return this.pipes
  }

  public checkForScore(birdX: number): number {
    let newScores = 0
    
    for (const pipe of this.pipes) {
      if (!pipe.scored && birdX > pipe.x + pipe.width) {
        pipe.scored = true
        newScores++
      }
    }
    
    return newScores
  }

  public reset(): void {
    // Return all active pipes to pool
    for (const pipe of this.pipes) {
      this.returnPipeToPool(pipe)
    }
    this.pipes = []
    this.speedMultiplier = 1
    this.gapReduction = 0
  }

  public setSpeedMultiplier(multiplier: number): void {
    this.speedMultiplier = multiplier
  }

  public setGapReduction(reduction: number): void {
    this.gapReduction = reduction
  }
}
