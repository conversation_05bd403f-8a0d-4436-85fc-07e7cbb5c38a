import './style.css'
import { Game } from './js/game'

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing game...')
  const canvas = document.getElementById('gameCanvas') as HTMLCanvasElement

  if (!canvas) {
    console.error('Canvas element not found!')
    return
  }

  console.log('Canvas found, creating game instance...')
  const game = new Game(canvas)
  game.init()
  console.log('Game initialized successfully!')
})
