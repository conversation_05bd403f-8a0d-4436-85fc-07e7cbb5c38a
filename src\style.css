/* Flappy Bird Game Styles */
:root {
  --sky-blue: #87CEEB;
  --ground-brown: #DEB887;
  --pipe-green: #228B22;
  --bird-yellow: #FFD700;
  --white: #FFFFFF;
  --black: #000000;
  --shadow: rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(to bottom, var(--sky-blue) 0%, var(--sky-blue) 70%, var(--ground-brown) 70%, var(--ground-brown) 100%);
  margin: 0;
  padding: 0;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

#gameCanvas {
  border: 2px solid var(--black);
  background: linear-gradient(to bottom, var(--sky-blue) 0%, var(--sky-blue) 80%, var(--ground-brown) 80%, var(--ground-brown) 100%);
  display: block;
  width: 800px;
  height: 600px;
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  z-index: 1;
}

#ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.screen {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--white);
  text-shadow: 2px 2px 4px var(--shadow);
  pointer-events: auto;
}

.hidden {
  display: none !important;
}

#startScreen h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--bird-yellow);
  text-shadow: 3px 3px 6px var(--shadow);
}

#startScreen p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.high-score {
  font-size: 1rem;
  color: var(--white);
  margin-top: 1rem;
}

#gameOverScreen h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #FF6B6B;
  text-shadow: 3px 3px 6px var(--shadow);
}

#pauseScreen h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--bird-yellow);
  text-shadow: 3px 3px 6px var(--shadow);
}

.pause-hint {
  font-size: 0.9rem;
  margin-top: 1rem;
  opacity: 0.8;
}

.score {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--bird-yellow);
}

#gameUI {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}

#score {
  font-size: 3rem;
  font-weight: bold;
  color: var(--white);
  text-shadow: 3px 3px 6px var(--shadow);
  text-align: center;
}

.difficulty-indicator {
  font-size: 1rem;
  color: var(--bird-yellow);
  text-shadow: 2px 2px 4px var(--shadow);
  text-align: center;
  margin-top: 10px;
  opacity: 0.8;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  #startScreen h1 {
    font-size: 2.5rem;
  }

  #startScreen p {
    font-size: 1rem;
  }

  #gameOverScreen h2 {
    font-size: 2rem;
  }

  #score {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  #startScreen h1 {
    font-size: 2rem;
  }

  #score {
    font-size: 2rem;
  }
}
