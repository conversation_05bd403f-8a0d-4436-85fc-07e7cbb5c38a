/* Flappy Bird Game Styles */
:root {
  --sky-blue: #87CEEB;
  --ground-brown: #DEB887;
  --pipe-green: #228B22;
  --bird-yellow: #FFD700;
  --white: #FFFFFF;
  --black: #000000;
  --shadow: rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(to bottom, var(--sky-blue) 0%, var(--sky-blue) 70%, var(--ground-brown) 70%, var(--ground-brown) 100%);
  margin: 0;
  padding: 0;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

#gameCanvas {
  border: 2px solid var(--black);
  background: linear-gradient(to bottom, var(--sky-blue) 0%, var(--sky-blue) 80%, var(--ground-brown) 80%, var(--ground-brown) 100%);
  display: block;
  width: 800px;
  height: 600px;
  max-width: 95vw;
  max-height: 85vh;
  position: relative;
  z-index: 1;
  touch-action: manipulation;
  cursor: pointer;
}

#ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.screen {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--white);
  text-shadow: 2px 2px 4px var(--shadow);
  pointer-events: auto;
}

.hidden {
  display: none !important;
}

#startScreen h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--bird-yellow);
  text-shadow: 3px 3px 6px var(--shadow);
}

#startScreen p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.high-score {
  font-size: 1rem;
  color: var(--white);
  margin-top: 1rem;
}

#gameOverScreen h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #FF6B6B;
  text-shadow: 3px 3px 6px var(--shadow);
}

#pauseScreen h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--bird-yellow);
  text-shadow: 3px 3px 6px var(--shadow);
}

.pause-hint {
  font-size: 0.9rem;
  margin-top: 1rem;
  opacity: 0.8;
}

.score {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--bird-yellow);
}

#gameUI {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}

#score {
  font-size: 3rem;
  font-weight: bold;
  color: var(--white);
  text-shadow: 3px 3px 6px var(--shadow);
  text-align: center;
}

.difficulty-indicator {
  font-size: 1rem;
  color: var(--bird-yellow);
  text-shadow: 2px 2px 4px var(--shadow);
  text-align: center;
  margin-top: 10px;
  opacity: 0.8;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  #app {
    padding: 10px;
  }

  #gameCanvas {
    max-width: 98vw;
    max-height: 80vh;
    border-width: 1px;
  }

  #startScreen h1 {
    font-size: 2.5rem;
  }

  #startScreen p {
    font-size: 1.1rem;
  }

  #gameOverScreen h2 {
    font-size: 2rem;
  }

  #score {
    font-size: 2.5rem;
  }

  .audio-controls {
    top: 15px;
    left: 15px;
    gap: 8px;
  }

  .audio-btn {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  #gameCanvas {
    max-width: 100vw;
    max-height: 75vh;
  }

  #startScreen h1 {
    font-size: 2rem;
  }

  #startScreen p {
    font-size: 1rem;
  }

  #score {
    font-size: 2rem;
  }

  .audio-controls {
    top: 10px;
    left: 10px;
    gap: 6px;
  }

  .audio-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .difficulty-indicator {
    font-size: 0.9rem;
  }

  .controls-hint {
    bottom: 10px;
  }

  .controls-hint p {
    font-size: 0.7rem;
    padding: 6px 10px;
  }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
  #gameCanvas {
    max-height: 90vh;
    max-width: 85vw;
  }

  #startScreen h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
  }

  #startScreen p {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .high-score {
    font-size: 0.8rem;
    margin-top: 0.5rem;
  }

  #score {
    font-size: 1.8rem;
  }
}

/* Audio Controls */
.audio-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  gap: 10px;
  pointer-events: auto;
  z-index: 1000;
}

.audio-btn {
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #87CEEB;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.audio-btn:hover {
  background: rgba(135, 206, 235, 0.2);
  transform: scale(1.1);
}

.audio-btn:active {
  transform: scale(0.95);
}

.audio-btn.disabled {
  opacity: 0.5;
  border-color: #666;
}

.audio-btn.disabled:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: none;
}

/* Controls hint */
.controls-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
  z-index: 1000;
}

.controls-hint p {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  border-radius: 15px;
  margin: 0;
  white-space: nowrap;
}

/* Focus styles for accessibility */
.audio-btn:focus {
  outline: 2px solid #87CEEB;
  outline-offset: 2px;
}

button:focus-visible {
  outline: 2px solid #87CEEB;
  outline-offset: 2px;
}
