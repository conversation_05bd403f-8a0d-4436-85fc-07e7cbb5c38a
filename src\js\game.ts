import { Bird } from './bird'
import { Pi<PERSON>Manager } from './pipes'
import { CollisionDetector } from './collision'
import { AudioManager } from './audio'
import { ParticleSystem } from './particles'
import { EffectsManager } from './effects'
import { BackgroundManager } from './background'

export enum GameState {
  START = 'start',
  PLAYING = 'playing',
  PAUSED = 'paused',
  GAME_OVER = 'gameOver'
}

export class Game {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private bird: Bird
  private pipeManager: PipeManager
  private collisionDetector: CollisionDetector
  private audioManager: AudioManager
  private particleSystem: ParticleSystem
  private effectsManager: EffectsManager
  private backgroundManager: BackgroundManager
  
  private gameState: GameState = GameState.START
  private score: number = 0
  private highScore: number = 0
  private difficultyLevel: number = 1
  private consecutivePipes: number = 0
  
  private lastTime: number = 0
  private animationId: number = 0
  
  // Game constants
  private readonly CANVAS_WIDTH = 800
  private readonly CANVAS_HEIGHT = 600
  private readonly GROUND_HEIGHT = 100
  
  // UI elements
  private startScreen: HTMLElement
  private gameOverScreen: HTMLElement
  private pauseScreen: HTMLElement
  private gameUI: HTMLElement
  private scoreElement: HTMLElement
  private finalScoreElement: HTMLElement
  private difficultyElement: HTMLElement
  private highScoreElements: NodeListOf<HTMLElement>
  private soundToggleBtn: HTMLElement
  private musicToggleBtn: HTMLElement

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')!
    
    // Set canvas size
    this.canvas.width = this.CANVAS_WIDTH
    this.canvas.height = this.CANVAS_HEIGHT
    
    // Initialize game objects
    this.bird = new Bird(this.CANVAS_WIDTH / 4, this.CANVAS_HEIGHT / 3)
    this.pipeManager = new PipeManager(this.CANVAS_WIDTH, this.CANVAS_HEIGHT, this.GROUND_HEIGHT)
    this.collisionDetector = new CollisionDetector()
    this.audioManager = new AudioManager()
    this.particleSystem = new ParticleSystem()
    this.effectsManager = new EffectsManager()
    this.backgroundManager = new BackgroundManager(this.CANVAS_WIDTH, this.CANVAS_HEIGHT, this.GROUND_HEIGHT)
    
    // Get UI elements
    this.startScreen = document.getElementById('startScreen')!
    this.gameOverScreen = document.getElementById('gameOverScreen')!
    this.pauseScreen = document.getElementById('pauseScreen')!
    this.gameUI = document.getElementById('gameUI')!
    this.scoreElement = document.getElementById('score')!
    this.finalScoreElement = document.getElementById('finalScore')!
    this.difficultyElement = document.getElementById('difficultyLevel')!
    this.highScoreElements = document.querySelectorAll('#highScore, #gameOverHighScore')
    this.soundToggleBtn = document.getElementById('soundToggle')!
    this.musicToggleBtn = document.getElementById('musicToggle')!
    
    // Load high score
    this.loadHighScore()
    this.updateHighScoreDisplay()
  }

  public init(): void {
    this.setupEventListeners()
    this.setupAudioControls()
    this.showStartScreen()
    this.gameLoop(0)
  }

  private setupEventListeners(): void {
    // Keyboard controls
    document.addEventListener('keydown', (e) => {
      if (e.code === 'Space') {
        e.preventDefault()
        this.handleInput()
      } else if (e.code === 'KeyP') {
        e.preventDefault()
        this.togglePause()
      }
    })
    
    // Mouse/touch controls
    this.canvas.addEventListener('click', () => this.handleInput())
    this.canvas.addEventListener('touchstart', (e) => {
      e.preventDefault()
      this.handleInput()
    })
    
    // Prevent context menu on right click
    this.canvas.addEventListener('contextmenu', (e) => e.preventDefault())
  }

  private setupAudioControls(): void {
    // Sound effects toggle
    this.soundToggleBtn.addEventListener('click', () => {
      const enabled = !this.audioManager.isEnabled()
      this.audioManager.setEnabled(enabled)
      this.updateAudioButtonStates()
    })

    // Background music toggle
    this.musicToggleBtn.addEventListener('click', () => {
      const enabled = !this.audioManager.isMusicEnabled()
      this.audioManager.setMusicEnabled(enabled)
      this.updateAudioButtonStates()
    })

    // Initialize button states
    this.updateAudioButtonStates()
  }

  private updateAudioButtonStates(): void {
    // Update sound effects button
    if (this.audioManager.isEnabled()) {
      this.soundToggleBtn.textContent = '🔊'
      this.soundToggleBtn.classList.remove('disabled')
      this.soundToggleBtn.title = 'Disable Sound Effects'
    } else {
      this.soundToggleBtn.textContent = '🔇'
      this.soundToggleBtn.classList.add('disabled')
      this.soundToggleBtn.title = 'Enable Sound Effects'
    }

    // Update music button
    if (this.audioManager.isMusicEnabled()) {
      this.musicToggleBtn.textContent = '🎵'
      this.musicToggleBtn.classList.remove('disabled')
      this.musicToggleBtn.title = 'Disable Background Music'
    } else {
      this.musicToggleBtn.textContent = '🔇'
      this.musicToggleBtn.classList.add('disabled')
      this.musicToggleBtn.title = 'Enable Background Music'
    }
  }

  private handleInput(): void {
    switch (this.gameState) {
      case GameState.START:
        this.startGame()
        break
      case GameState.PLAYING:
        this.bird.jump()
        this.audioManager.playJump()

        // Add jump particle effect
        const birdCenter = { x: this.bird.x + this.bird.width / 2, y: this.bird.y + this.bird.height / 2 }
        this.particleSystem.createSparkles(birdCenter.x, birdCenter.y, 2)
        break
      case GameState.PAUSED:
        this.resumeGame()
        break
      case GameState.GAME_OVER:
        this.resetGame()
        break
    }
  }

  private togglePause(): void {
    if (this.gameState === GameState.PLAYING) {
      this.pauseGame()
    } else if (this.gameState === GameState.PAUSED) {
      this.resumeGame()
    }
  }

  private pauseGame(): void {
    this.gameState = GameState.PAUSED
    this.hideAllScreens()
    this.pauseScreen.classList.remove('hidden')
  }

  private resumeGame(): void {
    this.gameState = GameState.PLAYING
    this.hideAllScreens()
    this.gameUI.classList.remove('hidden')
  }

  private startGame(): void {
    this.gameState = GameState.PLAYING
    this.score = 0
    this.difficultyLevel = 1
    this.consecutivePipes = 0
    this.bird.reset(this.CANVAS_WIDTH / 4, this.CANVAS_HEIGHT / 3)
    this.pipeManager.reset()
    this.particleSystem.clear()
    this.backgroundManager.reset()
    this.updateDifficulty()
    this.hideAllScreens()
    this.gameUI.classList.remove('hidden')
    this.updateScoreDisplay()
    this.updateDifficultyDisplay()

    // Start background music
    this.audioManager.startBackgroundMusic()
  }

  private resetGame(): void {
    this.gameState = GameState.START
    this.showStartScreen()
  }

  private gameOver(): void {
    this.gameState = GameState.GAME_OVER
    this.audioManager.playHit()

    // Stop background music
    this.audioManager.stopBackgroundMusic()

    // Update high score
    if (this.score > this.highScore) {
      this.highScore = this.score
      this.saveHighScore()
      this.updateHighScoreDisplay()
    }

    this.hideAllScreens()
    this.finalScoreElement.textContent = this.score.toString()
    this.gameOverScreen.classList.remove('hidden')
  }

  private showStartScreen(): void {
    this.hideAllScreens()
    this.startScreen.classList.remove('hidden')
  }

  private hideAllScreens(): void {
    this.startScreen.classList.add('hidden')
    this.gameOverScreen.classList.add('hidden')
    this.pauseScreen.classList.add('hidden')
    this.gameUI.classList.add('hidden')
  }

  private updateScoreDisplay(): void {
    this.scoreElement.textContent = this.score.toString()
  }

  private updateDifficultyDisplay(): void {
    this.difficultyElement.textContent = `Level ${this.difficultyLevel}`
  }

  private updateHighScoreDisplay(): void {
    this.highScoreElements.forEach(element => {
      element.textContent = this.highScore.toString()
    })
  }

  private loadHighScore(): void {
    const saved = localStorage.getItem('flappyBirdHighScore')
    this.highScore = saved ? parseInt(saved, 10) : 0
  }

  private saveHighScore(): void {
    localStorage.setItem('flappyBirdHighScore', this.highScore.toString())
  }

  private checkDifficultyProgression(): void {
    // Increase difficulty every 5 points
    const newDifficultyLevel = Math.floor(this.score / 5) + 1
    if (newDifficultyLevel > this.difficultyLevel) {
      this.difficultyLevel = newDifficultyLevel
      this.updateDifficulty()
      this.updateDifficultyDisplay()
      this.showDifficultyIncrease()
    }
  }

  private updateDifficulty(): void {
    // Increase pipe speed (max 50% increase)
    const speedMultiplier = Math.min(1 + (this.difficultyLevel - 1) * 0.1, 1.5)
    this.pipeManager.setSpeedMultiplier(speedMultiplier)

    // Decrease pipe gap slightly (max 20% decrease)
    const gapReduction = Math.min((this.difficultyLevel - 1) * 10, 40)
    this.pipeManager.setGapReduction(gapReduction)
  }

  private showDifficultyIncrease(): void {
    // Visual feedback for difficulty increase
    console.log(`Difficulty increased to level ${this.difficultyLevel}!`)

    // Create visual effects for difficulty increase
    const centerX = this.CANVAS_WIDTH / 2
    const centerY = this.CANVAS_HEIGHT / 3

    this.particleSystem.createSparkles(centerX, centerY, 8)
    this.effectsManager.flash(0.3, 400, '#87CEEB')
    this.effectsManager.shake(3, 200)
    this.audioManager.playLevelUp()
  }

  private update(deltaTime: number): void {
    if (this.gameState !== GameState.PLAYING) return
    
    // Update game objects
    this.bird.update(deltaTime)
    this.pipeManager.update(deltaTime)
    this.particleSystem.update(deltaTime)
    this.effectsManager.update(deltaTime)
    this.backgroundManager.update(deltaTime)
    
    // Check for scoring
    const passedPipes = this.pipeManager.checkForScore(this.bird.x)
    if (passedPipes > 0) {
      this.consecutivePipes += passedPipes

      // Calculate score with bonus for consecutive pipes
      let points = passedPipes
      if (this.consecutivePipes >= 3) {
        points += Math.floor(this.consecutivePipes / 3) // Bonus point every 3 consecutive pipes
      }

      this.score += points
      this.updateScoreDisplay()
      this.audioManager.playScore()

      // Create visual effects for scoring
      const birdCenter = { x: this.bird.x + this.bird.width / 2, y: this.bird.y + this.bird.height / 2 }
      this.particleSystem.createScoreEffect(birdCenter.x, birdCenter.y)
      this.particleSystem.createSparkles(birdCenter.x, birdCenter.y)
      this.effectsManager.showScorePopup(birdCenter.x, birdCenter.y, points)

      // Flash effect for bonus points
      if (points > 1) {
        this.effectsManager.flash(0.2, 150, '#FFD700')
      }

      // Check for difficulty progression
      this.checkDifficultyProgression()
    }
    
    // Check collisions
    const pipes = this.pipeManager.getPipes()
    if (this.collisionDetector.checkCollisions(this.bird, pipes, this.CANVAS_HEIGHT - this.GROUND_HEIGHT)) {
      this.consecutivePipes = 0

      // Create collision effects
      const birdCenter = { x: this.bird.x + this.bird.width / 2, y: this.bird.y + this.bird.height / 2 }
      this.particleSystem.createExplosion(birdCenter.x, birdCenter.y, 12)
      this.effectsManager.shake(8, 400)
      this.effectsManager.flash(0.4, 300, '#FF6B6B')

      this.gameOver()
    }
  }

  private render(): void {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.CANVAS_WIDTH, this.CANVAS_HEIGHT)

    // Apply screen shake if active
    const shakeOffset = this.effectsManager.getShakeOffset()
    if (this.effectsManager.isShaking()) {
      this.ctx.save()
      this.ctx.translate(shakeOffset.x, shakeOffset.y)
    }

    // Draw enhanced background
    this.backgroundManager.renderSky(this.ctx)
    this.backgroundManager.renderClouds(this.ctx)

    // Draw game objects
    this.pipeManager.render(this.ctx)
    this.bird.render(this.ctx)
    this.particleSystem.render(this.ctx)

    // Draw enhanced ground
    this.backgroundManager.renderGround(this.ctx)

    // Restore context if shake was applied
    if (this.effectsManager.isShaking()) {
      this.ctx.restore()
    }

    // Render effects that should be on top (flash, score popup)
    this.effectsManager.render(this.ctx, this.CANVAS_WIDTH, this.CANVAS_HEIGHT)
  }



  private gameLoop(currentTime: number): void {
    const deltaTime = currentTime - this.lastTime
    this.lastTime = currentTime
    
    this.update(deltaTime)
    this.render()
    
    this.animationId = requestAnimationFrame((time) => this.gameLoop(time))
  }

  public destroy(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  }
}
