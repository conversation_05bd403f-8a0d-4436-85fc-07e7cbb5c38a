import { <PERSON> } from './bird'
import { PipeManager } from './pipes'
import { CollisionDetector } from './collision'
import { AudioManager } from './audio'
import { ParticleSystem } from './particles'
import { EffectsManager } from './effects'
import { BackgroundManager } from './background'

export enum GameState {
  START = 'start',
  PLAYING = 'playing',
  PAUSED = 'paused',
  GAME_OVER = 'gameOver'
}

export class Game {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private bird: Bird
  private pipeManager: PipeManager
  private collisionDetector: CollisionDetector
  private audioManager: AudioManager
  private particleSystem: ParticleSystem
  private effectsManager: EffectsManager
  private backgroundManager: BackgroundManager
  
  private gameState: GameState = GameState.START
  private score: number = 0
  private highScore: number = 0
  private difficultyLevel: number = 1
  private consecutivePipes: number = 0
  
  private lastTime: number = 0
  private animationId: number = 0

  // Performance monitoring
  private frameCount: number = 0
  private lastFpsTime: number = 0
  private currentFps: number = 0
  private targetFps: number = 60
  private frameTimeThreshold: number = 1000 / 50 // 50 FPS threshold

  // Performance optimization flags
  private isLowPerformanceMode: boolean = false
  private skipParticleEffects: boolean = false

  // Game constants
  private readonly CANVAS_WIDTH = 800
  private readonly CANVAS_HEIGHT = 600
  private readonly GROUND_HEIGHT = 100
  
  // UI elements
  private startScreen: HTMLElement
  private gameOverScreen: HTMLElement
  private pauseScreen: HTMLElement
  private gameUI: HTMLElement
  private scoreElement: HTMLElement
  private finalScoreElement: HTMLElement
  private difficultyElement: HTMLElement
  private highScoreElements: NodeListOf<HTMLElement>
  private soundToggleBtn: HTMLElement
  private musicToggleBtn: HTMLElement
  private fullscreenToggleBtn: HTMLElement

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')!
    
    // Set canvas size
    this.canvas.width = this.CANVAS_WIDTH
    this.canvas.height = this.CANVAS_HEIGHT
    
    // Initialize game objects
    this.bird = new Bird(this.CANVAS_WIDTH / 4, this.CANVAS_HEIGHT / 3)
    this.pipeManager = new PipeManager(this.CANVAS_WIDTH, this.CANVAS_HEIGHT, this.GROUND_HEIGHT)
    this.collisionDetector = new CollisionDetector()
    this.audioManager = new AudioManager()
    this.particleSystem = new ParticleSystem()
    this.effectsManager = new EffectsManager()
    this.backgroundManager = new BackgroundManager(this.CANVAS_WIDTH, this.CANVAS_HEIGHT, this.GROUND_HEIGHT)
    
    // Get UI elements
    this.startScreen = document.getElementById('startScreen')!
    this.gameOverScreen = document.getElementById('gameOverScreen')!
    this.pauseScreen = document.getElementById('pauseScreen')!
    this.gameUI = document.getElementById('gameUI')!
    this.scoreElement = document.getElementById('score')!
    this.finalScoreElement = document.getElementById('finalScore')!
    this.difficultyElement = document.getElementById('difficultyLevel')!
    this.highScoreElements = document.querySelectorAll('#highScore, #gameOverHighScore')
    this.soundToggleBtn = document.getElementById('soundToggle')!
    this.musicToggleBtn = document.getElementById('musicToggle')!
    this.fullscreenToggleBtn = document.getElementById('fullscreenToggle')!
    
    // Load high score
    this.loadHighScore()
    this.updateHighScoreDisplay()
  }

  public init(): void {
    this.setupEventListeners()
    this.setupAudioControls()
    this.showStartScreen()
    this.gameLoop(0)
  }

  private setupEventListeners(): void {
    // Keyboard controls
    document.addEventListener('keydown', (e) => {
      if (e.code === 'Space' || e.code === 'ArrowUp' || e.code === 'KeyW') {
        e.preventDefault()
        this.handleInput()
      } else if (e.code === 'KeyP' || e.code === 'Escape') {
        e.preventDefault()
        this.togglePause()
      } else if (e.code === 'KeyR' && this.gameState === GameState.GAME_OVER) {
        e.preventDefault()
        this.resetGame()
      } else if (e.code === 'KeyM') {
        e.preventDefault()
        this.toggleMute()
      } else if (e.code === 'KeyF') {
        e.preventDefault()
        this.toggleFullscreen()
      }
    })

    // Mouse controls
    this.canvas.addEventListener('click', () => this.handleInput())
    this.canvas.addEventListener('mousedown', (e) => {
      e.preventDefault()
    })

    // Enhanced touch controls
    this.canvas.addEventListener('touchstart', (e) => {
      e.preventDefault()
      this.handleInput()
    })

    this.canvas.addEventListener('touchmove', (e) => {
      e.preventDefault()
    })

    this.canvas.addEventListener('touchend', (e) => {
      e.preventDefault()
    })

    // Prevent context menu and selection
    this.canvas.addEventListener('contextmenu', (e) => e.preventDefault())
    this.canvas.addEventListener('selectstart', (e) => e.preventDefault())

    // Handle window focus/blur for pause
    window.addEventListener('blur', () => {
      if (this.gameState === GameState.PLAYING) {
        this.pauseGame()
      }
    })

    // Handle orientation change
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.handleResize()
      }, 100)
    })

    // Handle window resize
    window.addEventListener('resize', () => {
      this.handleResize()
    })

    // Handle fullscreen change
    document.addEventListener('fullscreenchange', () => {
      this.updateFullscreenButtonState()
      this.handleResize()
    })
  }

  private setupAudioControls(): void {
    // Sound effects toggle
    this.soundToggleBtn.addEventListener('click', () => {
      const enabled = !this.audioManager.isEnabled()
      this.audioManager.setEnabled(enabled)
      this.updateAudioButtonStates()
    })

    // Background music toggle
    this.musicToggleBtn.addEventListener('click', () => {
      const enabled = !this.audioManager.isMusicEnabled()
      this.audioManager.setMusicEnabled(enabled)
      this.updateAudioButtonStates()
    })

    // Fullscreen toggle
    this.fullscreenToggleBtn.addEventListener('click', () => {
      this.toggleFullscreen()
    })

    // Initialize button states
    this.updateAudioButtonStates()
    this.updateFullscreenButtonState()
  }

  private toggleMute(): void {
    const soundEnabled = !this.audioManager.isEnabled()
    this.audioManager.setEnabled(soundEnabled)

    const musicEnabled = !this.audioManager.isMusicEnabled()
    this.audioManager.setMusicEnabled(musicEnabled)

    this.updateAudioButtonStates()
  }

  private handleResize(): void {
    // Force canvas to maintain aspect ratio
    const canvas = this.canvas
    const container = canvas.parentElement
    if (!container) return

    const containerWidth = container.clientWidth
    const containerHeight = container.clientHeight
    const aspectRatio = this.CANVAS_WIDTH / this.CANVAS_HEIGHT

    let newWidth = containerWidth * 0.95
    let newHeight = newWidth / aspectRatio

    if (newHeight > containerHeight * 0.85) {
      newHeight = containerHeight * 0.85
      newWidth = newHeight * aspectRatio
    }

    canvas.style.width = `${newWidth}px`
    canvas.style.height = `${newHeight}px`
  }

  private toggleFullscreen(): void {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.log(`Error attempting to enable fullscreen: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }

  private updateFullscreenButtonState(): void {
    if (document.fullscreenElement) {
      this.fullscreenToggleBtn.textContent = '⛶'
      this.fullscreenToggleBtn.title = 'Exit Fullscreen'
    } else {
      this.fullscreenToggleBtn.textContent = '⛶'
      this.fullscreenToggleBtn.title = 'Enter Fullscreen'
    }
  }

  private updateAudioButtonStates(): void {
    // Update sound effects button
    if (this.audioManager.isEnabled()) {
      this.soundToggleBtn.textContent = '🔊'
      this.soundToggleBtn.classList.remove('disabled')
      this.soundToggleBtn.title = 'Disable Sound Effects'
    } else {
      this.soundToggleBtn.textContent = '🔇'
      this.soundToggleBtn.classList.add('disabled')
      this.soundToggleBtn.title = 'Enable Sound Effects'
    }

    // Update music button
    if (this.audioManager.isMusicEnabled()) {
      this.musicToggleBtn.textContent = '🎵'
      this.musicToggleBtn.classList.remove('disabled')
      this.musicToggleBtn.title = 'Disable Background Music'
    } else {
      this.musicToggleBtn.textContent = '🔇'
      this.musicToggleBtn.classList.add('disabled')
      this.musicToggleBtn.title = 'Enable Background Music'
    }
  }

  private handleInput(): void {
    switch (this.gameState) {
      case GameState.START:
        this.startGame()
        break
      case GameState.PLAYING:
        this.bird.jump()
        this.audioManager.playJump()

        // Add jump particle effect (skip if low performance mode)
        if (!this.skipParticleEffects) {
          const birdCenter = { x: this.bird.x + this.bird.width / 2, y: this.bird.y + this.bird.height / 2 }
          this.particleSystem.createSparkles(birdCenter.x, birdCenter.y, 2)
        }
        break
      case GameState.PAUSED:
        this.resumeGame()
        break
      case GameState.GAME_OVER:
        this.resetGame()
        break
    }
  }

  private togglePause(): void {
    if (this.gameState === GameState.PLAYING) {
      this.pauseGame()
    } else if (this.gameState === GameState.PAUSED) {
      this.resumeGame()
    }
  }

  private pauseGame(): void {
    this.gameState = GameState.PAUSED
    this.hideAllScreens()
    this.pauseScreen.classList.remove('hidden')
  }

  private resumeGame(): void {
    this.gameState = GameState.PLAYING
    this.hideAllScreens()
    this.gameUI.classList.remove('hidden')
  }

  private startGame(): void {
    this.gameState = GameState.PLAYING
    this.score = 0
    this.difficultyLevel = 1
    this.consecutivePipes = 0
    this.bird.reset(this.CANVAS_WIDTH / 4, this.CANVAS_HEIGHT / 3)
    this.pipeManager.reset()
    this.particleSystem.clear()
    this.backgroundManager.reset()
    this.updateDifficulty()
    this.hideAllScreens()
    this.gameUI.classList.remove('hidden')
    this.updateScoreDisplay()
    this.updateDifficultyDisplay()

    // Start background music
    this.audioManager.startBackgroundMusic()
  }

  private resetGame(): void {
    this.gameState = GameState.START
    this.showStartScreen()
  }

  private gameOver(): void {
    this.gameState = GameState.GAME_OVER
    this.audioManager.playHit()

    // Stop background music
    this.audioManager.stopBackgroundMusic()

    // Update high score
    if (this.score > this.highScore) {
      this.highScore = this.score
      this.saveHighScore()
      this.updateHighScoreDisplay()
    }

    this.hideAllScreens()
    this.finalScoreElement.textContent = this.score.toString()
    this.gameOverScreen.classList.remove('hidden')
  }

  private showStartScreen(): void {
    this.hideAllScreens()
    this.startScreen.classList.remove('hidden')
  }

  private hideAllScreens(): void {
    this.startScreen.classList.add('hidden')
    this.gameOverScreen.classList.add('hidden')
    this.pauseScreen.classList.add('hidden')
    this.gameUI.classList.add('hidden')
  }

  private updateScoreDisplay(): void {
    this.scoreElement.textContent = this.score.toString()
  }

  private updateDifficultyDisplay(): void {
    this.difficultyElement.textContent = `Level ${this.difficultyLevel}`
  }

  private updateHighScoreDisplay(): void {
    this.highScoreElements.forEach(element => {
      element.textContent = this.highScore.toString()
    })
  }

  private loadHighScore(): void {
    const saved = localStorage.getItem('flappyBirdHighScore')
    this.highScore = saved ? parseInt(saved, 10) : 0
  }

  private saveHighScore(): void {
    localStorage.setItem('flappyBirdHighScore', this.highScore.toString())
  }

  private checkDifficultyProgression(): void {
    // Increase difficulty every 5 points
    const newDifficultyLevel = Math.floor(this.score / 5) + 1
    if (newDifficultyLevel > this.difficultyLevel) {
      this.difficultyLevel = newDifficultyLevel
      this.updateDifficulty()
      this.updateDifficultyDisplay()
      this.showDifficultyIncrease()
    }
  }

  private updateDifficulty(): void {
    // Increase pipe speed (max 50% increase)
    const speedMultiplier = Math.min(1 + (this.difficultyLevel - 1) * 0.1, 1.5)
    this.pipeManager.setSpeedMultiplier(speedMultiplier)

    // Decrease pipe gap slightly (max 20% decrease)
    const gapReduction = Math.min((this.difficultyLevel - 1) * 10, 40)
    this.pipeManager.setGapReduction(gapReduction)
  }

  private showDifficultyIncrease(): void {
    // Visual feedback for difficulty increase
    console.log(`Difficulty increased to level ${this.difficultyLevel}!`)

    // Create visual effects for difficulty increase
    const centerX = this.CANVAS_WIDTH / 2
    const centerY = this.CANVAS_HEIGHT / 3

    this.particleSystem.createSparkles(centerX, centerY, 8)
    this.effectsManager.flash(0.3, 400, '#87CEEB')
    this.effectsManager.shake(3, 200)
    this.audioManager.playLevelUp()
  }

  private update(deltaTime: number): void {
    if (this.gameState !== GameState.PLAYING) return
    
    // Update game objects
    this.bird.update(deltaTime)
    this.pipeManager.update(deltaTime)
    this.particleSystem.update(deltaTime)
    this.effectsManager.update(deltaTime)
    this.backgroundManager.update(deltaTime)
    
    // Check for scoring
    const passedPipes = this.pipeManager.checkForScore(this.bird.x)
    if (passedPipes > 0) {
      this.consecutivePipes += passedPipes

      // Calculate score with bonus for consecutive pipes
      let points = passedPipes
      if (this.consecutivePipes >= 3) {
        points += Math.floor(this.consecutivePipes / 3) // Bonus point every 3 consecutive pipes
      }

      this.score += points
      this.updateScoreDisplay()
      this.audioManager.playScore()

      // Create visual effects for scoring (reduce in low performance mode)
      const birdCenter = { x: this.bird.x + this.bird.width / 2, y: this.bird.y + this.bird.height / 2 }
      if (!this.skipParticleEffects) {
        this.particleSystem.createScoreEffect(birdCenter.x, birdCenter.y)
        this.particleSystem.createSparkles(birdCenter.x, birdCenter.y)
      }
      this.effectsManager.showScorePopup(birdCenter.x, birdCenter.y, points)

      // Flash effect for bonus points (reduce in low performance mode)
      if (points > 1 && !this.isLowPerformanceMode) {
        this.effectsManager.flash(0.2, 150, '#FFD700')
      }

      // Check for difficulty progression
      this.checkDifficultyProgression()
    }
    
    // Check collisions
    const pipes = this.pipeManager.getPipes()
    if (this.collisionDetector.checkCollisions(this.bird, pipes, this.CANVAS_HEIGHT - this.GROUND_HEIGHT)) {
      this.consecutivePipes = 0

      // Create collision effects (reduce in low performance mode)
      const birdCenter = { x: this.bird.x + this.bird.width / 2, y: this.bird.y + this.bird.height / 2 }
      if (!this.skipParticleEffects) {
        this.particleSystem.createExplosion(birdCenter.x, birdCenter.y, this.isLowPerformanceMode ? 6 : 12)
      }
      this.effectsManager.shake(this.isLowPerformanceMode ? 4 : 8, 400)
      this.effectsManager.flash(0.4, 300, '#FF6B6B')

      this.gameOver()
    }
  }

  private render(): void {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.CANVAS_WIDTH, this.CANVAS_HEIGHT)

    // Apply screen shake if active
    const shakeOffset = this.effectsManager.getShakeOffset()
    if (this.effectsManager.isShaking()) {
      this.ctx.save()
      this.ctx.translate(shakeOffset.x, shakeOffset.y)
    }

    // Draw enhanced background
    this.backgroundManager.renderSky(this.ctx)
    this.backgroundManager.renderClouds(this.ctx)

    // Draw game objects
    this.pipeManager.render(this.ctx)
    this.bird.render(this.ctx)
    this.particleSystem.render(this.ctx)

    // Draw enhanced ground
    this.backgroundManager.renderGround(this.ctx)

    // Restore context if shake was applied
    if (this.effectsManager.isShaking()) {
      this.ctx.restore()
    }

    // Render effects that should be on top (flash, score popup)
    this.effectsManager.render(this.ctx, this.CANVAS_WIDTH, this.CANVAS_HEIGHT)
  }



  private gameLoop(currentTime: number): void {
    const deltaTime = currentTime - this.lastTime
    this.lastTime = currentTime

    // Performance monitoring
    this.updateFpsCounter(currentTime)

    // Skip frame if performance is poor (adaptive frame rate)
    if (deltaTime > this.frameTimeThreshold) {
      console.warn(`Frame time exceeded threshold: ${deltaTime.toFixed(2)}ms`)
      // Still update game state but skip rendering for this frame
      this.update(deltaTime)
    } else {
      this.update(deltaTime)
      this.render()
    }

    this.animationId = requestAnimationFrame((time) => this.gameLoop(time))
  }

  private updateFpsCounter(currentTime: number): void {
    this.frameCount++

    if (currentTime - this.lastFpsTime >= 1000) {
      this.currentFps = this.frameCount
      this.frameCount = 0
      this.lastFpsTime = currentTime

      // Automatically enable performance optimizations if FPS is low
      if (this.currentFps < this.targetFps * 0.6) {
        if (!this.isLowPerformanceMode) {
          console.warn(`Enabling low performance mode: ${this.currentFps} FPS (target: ${this.targetFps})`)
          this.isLowPerformanceMode = true
          this.skipParticleEffects = true
        }
      } else if (this.currentFps > this.targetFps * 0.9) {
        if (this.isLowPerformanceMode) {
          console.log(`Disabling low performance mode: ${this.currentFps} FPS`)
          this.isLowPerformanceMode = false
          this.skipParticleEffects = false
        }
      }

      // Log performance warnings
      if (this.currentFps < this.targetFps * 0.8) {
        console.warn(`Low FPS detected: ${this.currentFps} (target: ${this.targetFps})`)
      }
    }
  }

  public destroy(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  }
}
