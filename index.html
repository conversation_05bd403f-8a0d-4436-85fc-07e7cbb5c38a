<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0" />
    <meta name="theme-color" content="#87CEEB" />
    <meta name="description" content="Flappy Bird - A fun web-based clone of the classic game" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="mobile-web-app-capable" content="yes" />
    <title>Flappy Bird</title>
  </head>
  <body>
    <div id="app" role="application" aria-label="Flappy Bird Game">
      <canvas id="gameCanvas" role="img" aria-label="Game Canvas"></canvas>
      <div id="ui" aria-live="polite">
        <div id="startScreen" class="screen" role="dialog" aria-labelledby="start-title">
          <h1 id="start-title">Flappy Bird</h1>
          <p>Tap, click, or press SPACE to start</p>
          <div class="high-score" aria-label="Current high score">High Score: <span id="highScore">0</span></div>
        </div>
        <div id="gameOverScreen" class="screen hidden" role="dialog" aria-labelledby="gameover-title">
          <h2 id="gameover-title">Game Over</h2>
          <div class="score" aria-label="Final score">Score: <span id="finalScore">0</span></div>
          <div class="high-score" aria-label="High score">High Score: <span id="gameOverHighScore">0</span></div>
          <p>Tap, click, or press SPACE to restart</p>
        </div>
        <div id="pauseScreen" class="screen hidden" role="dialog" aria-labelledby="pause-title">
          <h2 id="pause-title">Paused</h2>
          <p>Press P or SPACE to resume</p>
          <p class="pause-hint">Press P during gameplay to pause</p>
        </div>
        <div id="gameUI" class="hidden" role="region" aria-label="Game interface">
          <div id="score" aria-label="Current score">0</div>
          <div id="difficultyLevel" class="difficulty-indicator" aria-label="Current difficulty level">Level 1</div>
          <div class="audio-controls" role="group" aria-label="Audio controls">
            <button id="soundToggle" class="audio-btn" title="Toggle Sound Effects" aria-label="Toggle sound effects">🔊</button>
            <button id="musicToggle" class="audio-btn" title="Toggle Background Music" aria-label="Toggle background music">🎵</button>
            <button id="fullscreenToggle" class="audio-btn" title="Toggle Fullscreen" aria-label="Toggle fullscreen mode">⛶</button>
          </div>
          <div class="controls-hint">
            <p>Controls: SPACE/TAP to jump • P to pause • M to mute • F for fullscreen</p>
          </div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
