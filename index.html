<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#87CEEB" />
    <meta name="description" content="Flappy Bird - A fun web-based clone of the classic game" />
    <title>Flappy Bird</title>
  </head>
  <body>
    <div id="app">
      <canvas id="gameCanvas"></canvas>
      <div id="ui">
        <div id="startScreen" class="screen">
          <h1>Flappy Bird</h1>
          <p>Click or press SPACE to start</p>
          <div class="high-score">High Score: <span id="highScore">0</span></div>
        </div>
        <div id="gameOverScreen" class="screen hidden">
          <h2>Game Over</h2>
          <div class="score">Score: <span id="finalScore">0</span></div>
          <div class="high-score">High Score: <span id="gameOverHighScore">0</span></div>
          <p>Click or press SPACE to restart</p>
        </div>
        <div id="pauseScreen" class="screen hidden">
          <h2>Paused</h2>
          <p>Press P or SPACE to resume</p>
          <p class="pause-hint">Press P during gameplay to pause</p>
        </div>
        <div id="gameUI" class="hidden">
          <div id="score">0</div>
          <div id="difficultyLevel" class="difficulty-indicator">Level 1</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
