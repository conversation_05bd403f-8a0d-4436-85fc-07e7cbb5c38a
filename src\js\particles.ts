export interface Particle {
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  size: number
  color: string
  type: 'explosion' | 'score' | 'sparkle'
}

export class ParticleSystem {
  private particles: Particle[] = []
  private particlePool: Particle[] = []
  private readonly MAX_POOL_SIZE = 50

  constructor() {
    this.initializePool()
  }

  private initializePool(): void {
    // Pre-create particle objects for object pooling
    for (let i = 0; i < this.MAX_POOL_SIZE; i++) {
      this.particlePool.push({
        x: 0,
        y: 0,
        vx: 0,
        vy: 0,
        life: 0,
        maxLife: 0,
        size: 0,
        color: '',
        type: 'explosion'
      })
    }
  }

  private getParticleFromPool(): Particle | null {
    return this.particlePool.pop() || null
  }

  private returnParticleToPool(particle: Particle): void {
    if (this.particlePool.length < this.MAX_POOL_SIZE) {
      this.particlePool.push(particle)
    }
  }

  public update(deltaTime: number): void {
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i]
      
      // Update position
      particle.x += particle.vx * deltaTime
      particle.y += particle.vy * deltaTime
      
      // Apply gravity for explosion particles
      if (particle.type === 'explosion') {
        particle.vy += 0.0002 * deltaTime
      }
      
      // Update life
      particle.life -= deltaTime
      
      // Remove dead particles and return to pool
      if (particle.life <= 0) {
        const deadParticle = this.particles.splice(i, 1)[0]
        this.returnParticleToPool(deadParticle)
      }
    }
  }

  public render(ctx: CanvasRenderingContext2D): void {
    for (const particle of this.particles) {
      const alpha = particle.life / particle.maxLife
      
      ctx.save()
      ctx.globalAlpha = alpha
      
      switch (particle.type) {
        case 'explosion':
          this.renderExplosionParticle(ctx, particle)
          break
        case 'score':
          this.renderScoreParticle(ctx, particle)
          break
        case 'sparkle':
          this.renderSparkleParticle(ctx, particle)
          break
      }
      
      ctx.restore()
    }
  }

  private renderExplosionParticle(ctx: CanvasRenderingContext2D, particle: Particle): void {
    ctx.fillStyle = particle.color
    ctx.beginPath()
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
    ctx.fill()
  }

  private renderScoreParticle(ctx: CanvasRenderingContext2D, particle: Particle): void {
    ctx.fillStyle = particle.color
    ctx.strokeStyle = '#FFFFFF'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()
  }

  private renderSparkleParticle(ctx: CanvasRenderingContext2D, particle: Particle): void {
    const size = particle.size
    ctx.fillStyle = particle.color
    ctx.strokeStyle = particle.color
    ctx.lineWidth = 2
    
    // Draw sparkle as a star shape
    ctx.beginPath()
    ctx.moveTo(particle.x, particle.y - size)
    ctx.lineTo(particle.x, particle.y + size)
    ctx.moveTo(particle.x - size, particle.y)
    ctx.lineTo(particle.x + size, particle.y)
    ctx.stroke()
  }

  public createExplosion(x: number, y: number, count: number = 8): void {
    for (let i = 0; i < count; i++) {
      const angle = (Math.PI * 2 * i) / count + Math.random() * 0.5
      const speed = 0.1 + Math.random() * 0.2

      // Try to get particle from pool, create new one if pool is empty
      let particle = this.getParticleFromPool()
      if (!particle) {
        particle = {
          x: x,
          y: y,
          vx: Math.cos(angle) * speed,
          vy: Math.sin(angle) * speed,
          life: 1000 + Math.random() * 500,
          maxLife: 1500,
          size: 3 + Math.random() * 4,
          color: this.getRandomExplosionColor(),
          type: 'explosion'
        }
      } else {
        // Reuse existing particle object
        particle.x = x
        particle.y = y
        particle.vx = Math.cos(angle) * speed
        particle.vy = Math.sin(angle) * speed
        particle.life = 1000 + Math.random() * 500
        particle.maxLife = 1500
        particle.size = 3 + Math.random() * 4
        particle.color = this.getRandomExplosionColor()
        particle.type = 'explosion'
      }

      this.particles.push(particle)
    }
  }

  public createScoreEffect(x: number, y: number, count: number = 5): void {
    for (let i = 0; i < count; i++) {
      const angle = Math.random() * Math.PI * 2
      const speed = 0.05 + Math.random() * 0.1

      // Try to get particle from pool, create new one if pool is empty
      let particle = this.getParticleFromPool()
      if (!particle) {
        particle = {
          x: x + (Math.random() - 0.5) * 40,
          y: y + (Math.random() - 0.5) * 20,
          vx: Math.cos(angle) * speed,
          vy: Math.sin(angle) * speed - 0.1, // Slight upward bias
          life: 800 + Math.random() * 400,
          maxLife: 1200,
          size: 2 + Math.random() * 3,
          color: '#FFD700', // Golden
          type: 'score'
        }
      } else {
        // Reuse existing particle object
        particle.x = x + (Math.random() - 0.5) * 40
        particle.y = y + (Math.random() - 0.5) * 20
        particle.vx = Math.cos(angle) * speed
        particle.vy = Math.sin(angle) * speed - 0.1
        particle.life = 800 + Math.random() * 400
        particle.maxLife = 1200
        particle.size = 2 + Math.random() * 3
        particle.color = '#FFD700'
        particle.type = 'score'
      }

      this.particles.push(particle)
    }
  }

  public createSparkles(x: number, y: number, count: number = 3): void {
    for (let i = 0; i < count; i++) {
      // Try to get particle from pool, create new one if pool is empty
      let particle = this.getParticleFromPool()
      if (!particle) {
        particle = {
          x: x + (Math.random() - 0.5) * 60,
          y: y + (Math.random() - 0.5) * 40,
          vx: (Math.random() - 0.5) * 0.02,
          vy: (Math.random() - 0.5) * 0.02,
          life: 600 + Math.random() * 400,
          maxLife: 1000,
          size: 3 + Math.random() * 2,
          color: this.getRandomSparkleColor(),
          type: 'sparkle'
        }
      } else {
        // Reuse existing particle object
        particle.x = x + (Math.random() - 0.5) * 60
        particle.y = y + (Math.random() - 0.5) * 40
        particle.vx = (Math.random() - 0.5) * 0.02
        particle.vy = (Math.random() - 0.5) * 0.02
        particle.life = 600 + Math.random() * 400
        particle.maxLife = 1000
        particle.size = 3 + Math.random() * 2
        particle.color = this.getRandomSparkleColor()
        particle.type = 'sparkle'
      }

      this.particles.push(particle)
    }
  }

  private getRandomExplosionColor(): string {
    const colors = ['#FF6B6B', '#FF8E53', '#FF6B35', '#F7931E', '#FFD23F']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  private getRandomSparkleColor(): string {
    const colors = ['#FFFFFF', '#FFD700', '#87CEEB', '#98FB98', '#DDA0DD']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  public clear(): void {
    // Return all active particles to pool
    for (const particle of this.particles) {
      this.returnParticleToPool(particle)
    }
    this.particles = []
  }

  public getParticleCount(): number {
    return this.particles.length
  }
}
