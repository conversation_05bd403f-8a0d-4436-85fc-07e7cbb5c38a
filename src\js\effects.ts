export class ScreenShake {
  private intensity: number = 0
  private duration: number = 0
  private maxDuration: number = 0
  private offsetX: number = 0
  private offsetY: number = 0

  public update(deltaTime: number): void {
    if (this.duration > 0) {
      this.duration -= deltaTime
      
      // Calculate shake intensity based on remaining duration
      const progress = this.duration / this.maxDuration
      const currentIntensity = this.intensity * progress
      
      // Generate random offset
      this.offsetX = (Math.random() - 0.5) * currentIntensity * 2
      this.offsetY = (Math.random() - 0.5) * currentIntensity * 2
      
      if (this.duration <= 0) {
        this.stop()
      }
    }
  }

  public start(intensity: number, duration: number): void {
    this.intensity = intensity
    this.duration = duration
    this.maxDuration = duration
  }

  public stop(): void {
    this.intensity = 0
    this.duration = 0
    this.offsetX = 0
    this.offsetY = 0
  }

  public getOffset(): { x: number, y: number } {
    return { x: this.offsetX, y: this.offsetY }
  }

  public isActive(): boolean {
    return this.duration > 0
  }
}

export class FlashEffect {
  private intensity: number = 0
  private duration: number = 0
  private maxDuration: number = 0
  private color: string = '#FFFFFF'

  public update(deltaTime: number): void {
    if (this.duration > 0) {
      this.duration -= deltaTime
      
      if (this.duration <= 0) {
        this.stop()
      }
    }
  }

  public start(intensity: number, duration: number, color: string = '#FFFFFF'): void {
    this.intensity = intensity
    this.duration = duration
    this.maxDuration = duration
    this.color = color
  }

  public stop(): void {
    this.intensity = 0
    this.duration = 0
  }

  public render(ctx: CanvasRenderingContext2D, width: number, height: number): void {
    if (this.duration > 0) {
      const progress = this.duration / this.maxDuration
      const alpha = this.intensity * progress
      
      ctx.save()
      ctx.globalAlpha = alpha
      ctx.fillStyle = this.color
      ctx.fillRect(0, 0, width, height)
      ctx.restore()
    }
  }

  public isActive(): boolean {
    return this.duration > 0
  }
}

export class ScorePopup {
  private x: number = 0
  private y: number = 0
  private text: string = ''
  private duration: number = 0
  private maxDuration: number = 0
  private startY: number = 0

  public update(deltaTime: number): void {
    if (this.duration > 0) {
      this.duration -= deltaTime
      
      // Float upward
      const progress = 1 - (this.duration / this.maxDuration)
      this.y = this.startY - progress * 50
      
      if (this.duration <= 0) {
        this.stop()
      }
    }
  }

  public start(x: number, y: number, text: string, duration: number = 1500): void {
    this.x = x
    this.y = y
    this.startY = y
    this.text = text
    this.duration = duration
    this.maxDuration = duration
  }

  public stop(): void {
    this.duration = 0
    this.text = ''
  }

  public render(ctx: CanvasRenderingContext2D): void {
    if (this.duration > 0 && this.text) {
      const progress = this.duration / this.maxDuration
      const alpha = progress
      const scale = 1 + (1 - progress) * 0.5
      
      ctx.save()
      ctx.globalAlpha = alpha
      ctx.translate(this.x, this.y)
      ctx.scale(scale, scale)
      
      // Draw text with outline
      ctx.font = 'bold 24px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      
      // Outline
      ctx.strokeStyle = '#000000'
      ctx.lineWidth = 3
      ctx.strokeText(this.text, 0, 0)
      
      // Fill
      ctx.fillStyle = '#FFD700'
      ctx.fillText(this.text, 0, 0)
      
      ctx.restore()
    }
  }

  public isActive(): boolean {
    return this.duration > 0
  }
}

export class EffectsManager {
  private screenShake: ScreenShake = new ScreenShake()
  private flashEffect: FlashEffect = new FlashEffect()
  private scorePopup: ScorePopup = new ScorePopup()

  public update(deltaTime: number): void {
    this.screenShake.update(deltaTime)
    this.flashEffect.update(deltaTime)
    this.scorePopup.update(deltaTime)
  }

  public render(ctx: CanvasRenderingContext2D, width: number, height: number): void {
    // Apply screen shake offset
    const shakeOffset = this.screenShake.getOffset()
    if (this.screenShake.isActive()) {
      ctx.save()
      ctx.translate(shakeOffset.x, shakeOffset.y)
    }

    // Note: Game objects should be rendered here by the caller
    
    if (this.screenShake.isActive()) {
      ctx.restore()
    }

    // Render flash effect
    this.flashEffect.render(ctx, width, height)
    
    // Render score popup
    this.scorePopup.render(ctx)
  }

  public shake(intensity: number = 5, duration: number = 300): void {
    this.screenShake.start(intensity, duration)
  }

  public flash(intensity: number = 0.3, duration: number = 200, color: string = '#FFFFFF'): void {
    this.flashEffect.start(intensity, duration, color)
  }

  public showScorePopup(x: number, y: number, points: number): void {
    this.scorePopup.start(x, y, `+${points}`, 1500)
  }

  public getShakeOffset(): { x: number, y: number } {
    return this.screenShake.getOffset()
  }

  public isShaking(): boolean {
    return this.screenShake.isActive()
  }
}
