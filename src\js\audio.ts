export class AudioManager {
  private audioContext: AudioContext | null = null
  private sounds: Map<string, AudioBuffer> = new Map()
  private enabled: boolean = true

  constructor() {
    this.initAudioContext()
    this.createSounds()
  }

  private initAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    } catch (error) {
      console.warn('Web Audio API not supported:', error)
      this.enabled = false
    }
  }

  private createSounds(): void {
    if (!this.audioContext || !this.enabled) return

    // Create jump sound (short beep)
    this.createBeepSound('jump', 200, 0.1, 'sine')
    
    // Create score sound (higher pitched beep)
    this.createBeepSound('score', 400, 0.15, 'sine')
    
    // Create hit sound (lower pitched noise)
    this.createNoiseSound('hit', 0.2)
  }

  private createBeepSound(name: string, frequency: number, duration: number, type: OscillatorType): void {
    if (!this.audioContext) return

    const sampleRate = this.audioContext.sampleRate
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      let sample = 0

      switch (type) {
        case 'sine':
          sample = Math.sin(2 * Math.PI * frequency * t)
          break
        case 'square':
          sample = Math.sin(2 * Math.PI * frequency * t) > 0 ? 1 : -1
          break
        case 'triangle':
          sample = (2 / Math.PI) * Math.asin(Math.sin(2 * Math.PI * frequency * t))
          break
      }

      // Apply envelope (fade out)
      const envelope = 1 - (t / duration)
      channelData[i] = sample * envelope * 0.3 // Volume control
    }

    this.sounds.set(name, buffer)
  }

  private createNoiseSound(name: string, duration: number): void {
    if (!this.audioContext) return

    const sampleRate = this.audioContext.sampleRate
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      // Generate brown noise (filtered white noise)
      const sample = (Math.random() * 2 - 1) * 0.1
      
      // Apply envelope
      const envelope = Math.exp(-t * 5) // Exponential decay
      channelData[i] = sample * envelope
    }

    this.sounds.set(name, buffer)
  }

  private playSound(name: string): void {
    if (!this.audioContext || !this.enabled) return

    const buffer = this.sounds.get(name)
    if (!buffer) return

    try {
      // Resume audio context if suspended (required by some browsers)
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      const source = this.audioContext.createBufferSource()
      source.buffer = buffer
      source.connect(this.audioContext.destination)
      source.start()
    } catch (error) {
      console.warn('Error playing sound:', error)
    }
  }

  public playJump(): void {
    this.playSound('jump')
  }

  public playScore(): void {
    this.playSound('score')
  }

  public playHit(): void {
    this.playSound('hit')
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled
  }

  public isEnabled(): boolean {
    return this.enabled
  }

  public destroy(): void {
    if (this.audioContext) {
      this.audioContext.close()
    }
    this.sounds.clear()
  }
}
