export class AudioManager {
  private audioContext: AudioContext | null = null
  private sounds: Map<string, AudioBuffer> = new Map()
  private enabled: boolean = true
  private musicEnabled: boolean = true
  private backgroundMusic: AudioBufferSourceNode | null = null
  private musicGainNode: GainNode | null = null

  constructor() {
    this.initAudioContext()
    this.createSounds()
    this.createBackgroundMusic()
  }

  private initAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // Create gain node for music volume control
      if (this.audioContext) {
        this.musicGainNode = this.audioContext.createGain()
        this.musicGainNode.gain.value = 0.3 // Lower volume for background music
        this.musicGainNode.connect(this.audioContext.destination)
      }
    } catch (error) {
      console.warn('Web Audio API not supported:', error)
      this.enabled = false
    }
  }

  private createSounds(): void {
    if (!this.audioContext || !this.enabled) return

    // Create jump sound (short beep)
    this.createBeepSound('jump', 200, 0.1, 'sine')
    
    // Create score sound (higher pitched beep)
    this.createBeepSound('score', 400, 0.15, 'sine')
    
    // Create hit sound (lower pitched noise)
    this.createNoiseSound('hit', 0.2)

    // Create level up sound (ascending tones)
    this.createLevelUpSound('levelup')
  }

  private createBeepSound(name: string, frequency: number, duration: number, type: OscillatorType): void {
    if (!this.audioContext) return

    const sampleRate = this.audioContext.sampleRate
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      let sample = 0

      switch (type) {
        case 'sine':
          sample = Math.sin(2 * Math.PI * frequency * t)
          break
        case 'square':
          sample = Math.sin(2 * Math.PI * frequency * t) > 0 ? 1 : -1
          break
        case 'triangle':
          sample = (2 / Math.PI) * Math.asin(Math.sin(2 * Math.PI * frequency * t))
          break
      }

      // Apply envelope (fade out)
      const envelope = 1 - (t / duration)
      channelData[i] = sample * envelope * 0.3 // Volume control
    }

    this.sounds.set(name, buffer)
  }

  private createNoiseSound(name: string, duration: number): void {
    if (!this.audioContext) return

    const sampleRate = this.audioContext.sampleRate
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      // Generate brown noise (filtered white noise)
      const sample = (Math.random() * 2 - 1) * 0.1
      
      // Apply envelope
      const envelope = Math.exp(-t * 5) // Exponential decay
      channelData[i] = sample * envelope
    }

    this.sounds.set(name, buffer)
  }

  private createLevelUpSound(name: string): void {
    if (!this.audioContext) return

    const sampleRate = this.audioContext.sampleRate
    const duration = 0.6
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    // Create ascending chord progression
    const frequencies = [261.63, 329.63, 392.00, 523.25] // C4, E4, G4, C5

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      let sample = 0

      // Play frequencies in sequence
      const noteIndex = Math.floor(t * 6) % frequencies.length
      const frequency = frequencies[noteIndex]

      sample = Math.sin(2 * Math.PI * frequency * t) * 0.3

      // Apply envelope
      const envelope = Math.exp(-t * 2) * (1 - t / duration)
      channelData[i] = sample * envelope
    }

    this.sounds.set(name, buffer)
  }

  private createBackgroundMusic(): void {
    if (!this.audioContext) return

    const sampleRate = this.audioContext.sampleRate
    const duration = 8 // 8 second loop
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    // Create a simple melody using pentatonic scale
    const notes = [
      { freq: 261.63, start: 0, duration: 1 },    // C4
      { freq: 293.66, start: 1, duration: 1 },    // D4
      { freq: 329.63, start: 2, duration: 1 },    // E4
      { freq: 392.00, start: 3, duration: 1 },    // G4
      { freq: 440.00, start: 4, duration: 1 },    // A4
      { freq: 392.00, start: 5, duration: 1 },    // G4
      { freq: 329.63, start: 6, duration: 1 },    // E4
      { freq: 261.63, start: 7, duration: 1 },    // C4
    ]

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      let sample = 0

      // Add each note at its designated time
      for (const note of notes) {
        const noteStart = note.start
        const noteEnd = note.start + note.duration

        if (t >= noteStart && t < noteEnd) {
          const noteTime = t - noteStart
          const envelope = Math.sin(Math.PI * noteTime / note.duration) * 0.5 // Bell curve envelope
          sample += Math.sin(2 * Math.PI * note.freq * noteTime) * envelope * 0.1
        }
      }

      channelData[i] = sample
    }

    this.sounds.set('backgroundMusic', buffer)
  }

  private playSound(name: string): void {
    if (!this.audioContext || !this.enabled) return

    const buffer = this.sounds.get(name)
    if (!buffer) return

    try {
      // Resume audio context if suspended (required by some browsers)
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      const source = this.audioContext.createBufferSource()
      source.buffer = buffer
      source.connect(this.audioContext.destination)
      source.start()
    } catch (error) {
      console.warn('Error playing sound:', error)
    }
  }

  public playJump(): void {
    this.playSound('jump')
  }

  public playScore(): void {
    this.playSound('score')
  }

  public playHit(): void {
    this.playSound('hit')
  }

  public playLevelUp(): void {
    this.playSound('levelup')
  }

  public startBackgroundMusic(): void {
    if (!this.audioContext || !this.musicEnabled || !this.enabled) return

    this.stopBackgroundMusic() // Stop any existing music

    const buffer = this.sounds.get('backgroundMusic')
    if (!buffer || !this.musicGainNode) return

    try {
      // Resume audio context if suspended
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }

      this.backgroundMusic = this.audioContext.createBufferSource()
      this.backgroundMusic.buffer = buffer
      this.backgroundMusic.loop = true
      this.backgroundMusic.connect(this.musicGainNode)
      this.backgroundMusic.start()
    } catch (error) {
      console.warn('Error playing background music:', error)
    }
  }

  public stopBackgroundMusic(): void {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.stop()
      } catch (error) {
        // Ignore errors when stopping already stopped sources
      }
      this.backgroundMusic = null
    }
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled
    if (!enabled) {
      this.stopBackgroundMusic()
    }
  }

  public setMusicEnabled(enabled: boolean): void {
    this.musicEnabled = enabled
    if (!enabled) {
      this.stopBackgroundMusic()
    } else if (this.enabled) {
      this.startBackgroundMusic()
    }
  }

  public isEnabled(): boolean {
    return this.enabled
  }

  public isMusicEnabled(): boolean {
    return this.musicEnabled
  }

  public destroy(): void {
    this.stopBackgroundMusic()
    if (this.audioContext) {
      this.audioContext.close()
    }
    this.sounds.clear()
  }
}
